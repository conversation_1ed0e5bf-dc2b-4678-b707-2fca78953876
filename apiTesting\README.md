# Game Save Data API - CRUD API with Global Ban System

A REST API built with Express.js for managing user save data across multiple games with a global ban system. When a user is banned in one game, they are automatically banned across all games.

## Getting Started

1. **API is deployed at:**
   ```
   https://blackrus.dev/api
   ```
   For local development, start the server with `node index.js` (runs on `http://localhost:8080`)

2. **Test the API:**
   ```bash
   node test-game-api.js
   ```

## API Endpoints

### Base URL: `https://blackrus.dev`

### 1. Get User Save Data for Specific Game
- **Method:** `GET`
- **Endpoint:** `/api/games/:gameId/users/:userId`
- **Description:** Retrieve a user's save data for a specific game
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "userId": "user1",
      "gameId": "game1",
      "IsBanned": false,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
  ```

### 2. Get All Users for Specific Game
- **Method:** `GET`
- **Endpoint:** `/api/games/:gameId/users`
- **Description:** Retrieve all users' save data for a specific game
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "user1": { ... },
      "user2": { ... }
    },
    "count": 2
  }
  ```

### 3. Get User Data Across All Games
- **Method:** `GET`
- **Endpoint:** `/api/users/:userId`
- **Description:** Retrieve a user's save data across all games
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "game1": { ... },
      "game2": { ... }
    },
    "count": 2,
    "isBanned": false
  }
  ```

### 4. Create/Update User Save Data
- **Method:** `POST`
- **Endpoint:** `/api/games/:gameId/users/:userId`
- **Description:** Create or update user save data for a specific game
- **Request Body:**
  ```json
  {
    "level": 5,
    "score": 1000,
    "inventory": ["sword", "potion"],
    "IsBanned": false
  }
  ```
- **Special Ban Behavior:** If `IsBanned: true` is sent, the user will be banned across ALL games
- **Response:**
  ```json
  {
    "success": true,
    "message": "Save data created successfully",
    "data": { ... }
  }
  ```

### 5. Update User Save Data
- **Method:** `PUT`
- **Endpoint:** `/api/games/:gameId/users/:userId`
- **Description:** Update existing user save data for a specific game
- **Request Body:**
  ```json
  {
    "level": 6,
    "score": 1200,
    "IsBanned": false
  }
  ```
- **Ban Control:** Setting `IsBanned: true/false` will ban/unban the user across ALL games
- **Response:**
  ```json
  {
    "success": true,
    "message": "Save data updated successfully",
    "data": { ... }
  }
  ```

### 6. Delete User Save Data for Specific Game
- **Method:** `DELETE`
- **Endpoint:** `/api/games/:gameId/users/:userId`
- **Description:** Delete user save data for a specific game
- **Response:**
  ```json
  {
    "success": true,
    "message": "Save data deleted successfully",
    "data": { ... }
  }
  ```

### 7. Delete User Data Across All Games
- **Method:** `DELETE`
- **Endpoint:** `/api/users/:userId`
- **Description:** Delete user save data across all games and remove from ban list
- **Response:**
  ```json
  {
    "success": true,
    "message": "User data deleted from 2 games",
    "data": { ... },
    "deletedCount": 2
  }
  ```

## Global Ban System

The API implements a global ban system where:
- Setting `IsBanned: true` for any user in any game will ban them across ALL games
- Setting `IsBanned: false` will unban them across ALL games
- Banned users cannot create or update save data (except for ban status changes)
- The `IsBanned` field is automatically maintained across all games for each user

## Example Usage with curl

```bash
# Create save data for user1 in game1
curl -X POST https://blackrus.dev/api/games/game1/users/user1 \
  -H "Content-Type: application/json" \
  -d '{"level":5,"score":1000,"inventory":["sword","potion"],"IsBanned":false}'

# Get user1's save data for game1
curl https://blackrus.dev/api/games/game1/users/user1

# Get all users for game1
curl https://blackrus.dev/api/games/game1/users

# Get user1's data across all games
curl https://blackrus.dev/api/users/user1

# Ban user1 (will propagate to all games)
curl -X PUT https://blackrus.dev/api/games/game1/users/user1 \
  -H "Content-Type: application/json" \
  -d '{"IsBanned":true}'

# Update save data (regular update)
curl -X PUT https://blackrus.dev/api/games/game1/users/user1 \
  -H "Content-Type: application/json" \
  -d '{"level":6,"score":1200}'

# Delete save data for specific game
curl -X DELETE https://blackrus.dev/api/games/game1/users/user1

# Delete user data across all games
curl -X DELETE https://blackrus.dev/api/users/user1
```

## Data Structure

Each save data entry has the following structure:
- `userId`: User identifier
- `gameId`: Game identifier
- `IsBanned`: Boolean indicating ban status (automatically managed)
- `createdAt`: Creation timestamp
- `updatedAt`: Last update timestamp
- `...`: Any additional game-specific save data

## Notes

- Data is stored in memory and will be lost when the server restarts
- For production use, consider using a database like MongoDB, PostgreSQL, or MySQL
- The global ban system ensures consistency across all games
- Banned users receive 403 Forbidden responses when trying to update save data
- The API includes comprehensive validation and error handling
- All responses follow a consistent JSON format with `success` field
