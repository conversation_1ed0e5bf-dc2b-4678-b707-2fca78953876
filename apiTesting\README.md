# API Testing - CRUD API

A simple REST API built with Express.js for storing, receiving, and updating data.

## Getting Started

1. **Start the server:**
   ```bash
   node index.js
   ```
   The server will run on `http://localhost:8080`

2. **Test the API:**
   ```bash
   node test-api.js
   ```

## API Endpoints

### Base URL: `http://localhost:8080`

### 1. Get All Data
- **Method:** `GET`
- **Endpoint:** `/api/data`
- **Description:** Retrieve all stored items
- **Response:**
  ```json
  {
    "success": true,
    "data": [...],
    "count": 2
  }
  ```

### 2. Get Item by ID
- **Method:** `GET`
- **Endpoint:** `/api/data/:id`
- **Description:** Retrieve a specific item by its ID
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "id": 1,
      "name": "Item Name",
      "description": "Item Description",
      "value": 42,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
  ```

### 3. Create New Item
- **Method:** `POST`
- **Endpoint:** `/api/data`
- **Description:** Create a new item
- **Request Body:**
  ```json
  {
    "name": "Item Name",        // Required
    "description": "Optional description",
    "value": 42                 // Optional
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "message": "Item created successfully",
    "data": { ... }
  }
  ```

### 4. Update Item
- **Method:** `PUT`
- **Endpoint:** `/api/data/:id`
- **Description:** Update an existing item
- **Request Body:**
  ```json
  {
    "name": "Updated Name",
    "description": "Updated description",
    "value": 100
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "message": "Item updated successfully",
    "data": { ... }
  }
  ```

### 5. Delete Item
- **Method:** `DELETE`
- **Endpoint:** `/api/data/:id`
- **Description:** Delete an item by ID
- **Response:**
  ```json
  {
    "success": true,
    "message": "Item deleted successfully",
    "data": { ... }
  }
  ```

## Example Usage with curl

```bash
# Get all items
curl http://localhost:8080/api/data

# Create a new item
curl -X POST http://localhost:8080/api/data \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Item","description":"A test","value":42}'

# Get item by ID
curl http://localhost:8080/api/data/1

# Update item
curl -X PUT http://localhost:8080/api/data/1 \
  -H "Content-Type: application/json" \
  -d '{"name":"Updated Item","value":100}'

# Delete item
curl -X DELETE http://localhost:8080/api/data/1
```

## Data Structure

Each item has the following structure:
- `id`: Unique identifier (auto-generated)
- `name`: Item name (required)
- `description`: Item description (optional)
- `value`: Item value (optional)
- `createdAt`: Creation timestamp
- `updatedAt`: Last update timestamp

## Notes

- Data is stored in memory and will be lost when the server restarts
- For production use, consider using a database like MongoDB, PostgreSQL, or MySQL
- The API includes basic validation and error handling
- All responses follow a consistent JSON format with `success` field
