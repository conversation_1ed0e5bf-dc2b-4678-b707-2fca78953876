{"name": "game-save-api", "version": "1.0.0", "description": "Game Save Data API with Global Ban System", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node index.js", "test": "node test-game-api.js"}, "keywords": ["api", "game", "save-data", "ban-system", "express"], "author": "blackrus.dev", "license": "ISC", "engines": {"node": ">=18.0.0"}, "dependencies": {"express": "^5.1.0"}, "devDependencies": {"node-fetch": "^3.3.2"}}