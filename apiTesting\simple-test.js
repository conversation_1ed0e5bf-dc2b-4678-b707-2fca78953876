// Simple test using built-in http module
const http = require('http');

const BASE_URL = 'http://localhost:8080';

function makeRequest(method, path, data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 8080,
            path: path,
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(body);
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

async function testAPI() {
    console.log('🚀 Testing API endpoints...\n');

    try {
        // Test 1: Get root endpoint
        console.log('1. Testing root endpoint:');
        let result = await makeRequest('GET', '/');
        console.log(`Status: ${result.status}`);
        console.log(JSON.stringify(result.data, null, 2));
        console.log('');

        // Test 2: Get all data (should be empty initially)
        console.log('2. Getting all data:');
        result = await makeRequest('GET', '/api/data');
        console.log(`Status: ${result.status}`);
        console.log(JSON.stringify(result.data, null, 2));
        console.log('');

        // Test 3: Create new item
        console.log('3. Creating new item:');
        result = await makeRequest('POST', '/api/data', {
            name: 'Test Item',
            description: 'This is a test item',
            value: 42
        });
        console.log(`Status: ${result.status}`);
        console.log(JSON.stringify(result.data, null, 2));
        const itemId = result.data.data ? result.data.data.id : null;
        console.log('');

        if (itemId) {
            // Test 4: Get specific item
            console.log('4. Getting specific item:');
            result = await makeRequest('GET', `/api/data/${itemId}`);
            console.log(`Status: ${result.status}`);
            console.log(JSON.stringify(result.data, null, 2));
            console.log('');

            // Test 5: Update item
            console.log('5. Updating item:');
            result = await makeRequest('PUT', `/api/data/${itemId}`, {
                name: 'Updated Test Item',
                description: 'This item has been updated',
                value: 100
            });
            console.log(`Status: ${result.status}`);
            console.log(JSON.stringify(result.data, null, 2));
            console.log('');

            // Test 6: Get all data again
            console.log('6. Getting all data after update:');
            result = await makeRequest('GET', '/api/data');
            console.log(`Status: ${result.status}`);
            console.log(JSON.stringify(result.data, null, 2));
            console.log('');

            // Test 7: Delete item
            console.log('7. Deleting item:');
            result = await makeRequest('DELETE', `/api/data/${itemId}`);
            console.log(`Status: ${result.status}`);
            console.log(JSON.stringify(result.data, null, 2));
            console.log('');
        }

        console.log('✅ All tests completed!');

    } catch (error) {
        console.error('❌ Error testing API:', error.message);
        console.log('Make sure your server is running with: node index.js');
    }
}

// Run the tests
testAPI();
