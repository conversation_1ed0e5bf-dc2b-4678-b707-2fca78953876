// Simple test script to demonstrate API usage
// Run this after starting your server with: node index.js

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const BASE_URL = 'http://localhost:8080';

async function testAPI() {
    console.log('🚀 Testing API endpoints...\n');

    try {
        // Test 1: Get all data (should be empty initially)
        console.log('1. Getting all data:');
        let response = await fetch(`${BASE_URL}/api/data`);
        let result = await response.json();
        console.log(JSON.stringify(result, null, 2));
        console.log('');

        // Test 2: Create new item
        console.log('2. Creating new item:');
        response = await fetch(`${BASE_URL}/api/data`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: 'Test Item',
                description: 'This is a test item',
                value: 42
            })
        });
        result = await response.json();
        console.log(JSON.stringify(result, null, 2));
        const itemId = result.data.id;
        console.log('');

        // Test 3: Get specific item
        console.log('3. Getting specific item:');
        response = await fetch(`${BASE_URL}/api/data/${itemId}`);
        result = await response.json();
        console.log(JSON.stringify(result, null, 2));
        console.log('');

        // Test 4: Update item
        console.log('4. Updating item:');
        response = await fetch(`${BASE_URL}/api/data/${itemId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: 'Updated Test Item',
                description: 'This item has been updated',
                value: 100
            })
        });
        result = await response.json();
        console.log(JSON.stringify(result, null, 2));
        console.log('');

        // Test 5: Get all data again (should show updated item)
        console.log('5. Getting all data after update:');
        response = await fetch(`${BASE_URL}/api/data`);
        result = await response.json();
        console.log(JSON.stringify(result, null, 2));
        console.log('');

        // Test 6: Delete item
        console.log('6. Deleting item:');
        response = await fetch(`${BASE_URL}/api/data/${itemId}`, {
            method: 'DELETE'
        });
        result = await response.json();
        console.log(JSON.stringify(result, null, 2));
        console.log('');

        // Test 7: Verify deletion
        console.log('7. Verifying deletion:');
        response = await fetch(`${BASE_URL}/api/data`);
        result = await response.json();
        console.log(JSON.stringify(result, null, 2));

        console.log('\n✅ All tests completed!');

    } catch (error) {
        console.error('❌ Error testing API:', error.message);
        console.log('Make sure your server is running with: node index.js');
    }
}

// Run the tests
testAPI();
