-- PlayerDataLoader.lua
-- Handles loading and saving player data from the Game Save API

local HttpService = game:GetService("HttpService")
local Players = game:GetService("Players")

-- Configuration
local API_BASE_URL = "https://blackrus.dev/api"
local GAME_ID = game.GameId or "default_game"

-- Player Data Manager
local PlayerDataManager = {}
PlayerDataManager.LoadedPlayers = {}

-- Default player data structure
local DEFAULT_PLAYER_DATA = {
    level = 1,
    score = 0,
    coins = 100,
    inventory = {},
    settings = {
        music = true,
        sfx = true
    },
    IsBanned = false
}

-- Helper function to make HTTP requests
local function makeAPIRequest(method, endpoint, data)
    local url = API_BASE_URL .. endpoint
    local success, result = pcall(function()
        if method == "GET" then
            return HttpService:GetAsync(url)
        elseif method == "POST" then
            return HttpService:PostAsync(url, HttpService:JSONEncode(data), Enum.HttpContentType.ApplicationJson)
        elseif method == "PUT" then
            return HttpService:RequestAsync({
                Url = url,
                Method = "PUT",
                Headers = {
                    ["Content-Type"] = "application/json"
                },
                Body = HttpService:JSONEncode(data)
            }).Body
        elseif method == "DELETE" then
            return HttpService:RequestAsync({
                Url = url,
                Method = "DELETE"
            }).Body
        end
    end)
    
    if success then
        local decoded = HttpService:JSONDecode(result)
        return decoded
    else
        warn("API Request failed: " .. tostring(result))
        return nil
    end
end

-- Load player data from API
function PlayerDataManager:LoadPlayerData(player)
    local userId = tostring(player.UserId)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    
    print("Loading data for player: " .. player.Name .. " (ID: " .. userId .. ")")
    
    local response = makeAPIRequest("GET", endpoint)
    
    if response and response.success then
        local playerData = response.data
        
        -- Check if player is banned
        if playerData.IsBanned then
            warn("Player " .. player.Name .. " is banned!")
            player:Kick("You have been banned from this game.")
            return nil
        end
        
        -- Store loaded data
        self.LoadedPlayers[userId] = playerData
        print("Successfully loaded data for " .. player.Name)
        return playerData
    else
        -- Player data doesn't exist, create default data
        print("No existing data found for " .. player.Name .. ", creating default data")
        return self:CreateDefaultPlayerData(player)
    end
end

-- Create default player data
function PlayerDataManager:CreateDefaultPlayerData(player)
    local userId = tostring(player.UserId)
    local defaultData = {}
    
    -- Copy default data
    for key, value in pairs(DEFAULT_PLAYER_DATA) do
        if type(value) == "table" then
            defaultData[key] = {}
            for k, v in pairs(value) do
                defaultData[key][k] = v
            end
        else
            defaultData[key] = value
        end
    end
    
    -- Save to API
    local success = self:SavePlayerData(player, defaultData)
    if success then
        self.LoadedPlayers[userId] = defaultData
        return defaultData
    else
        warn("Failed to create default data for " .. player.Name)
        return nil
    end
end

-- Save player data to API
function PlayerDataManager:SavePlayerData(player, data)
    local userId = tostring(player.UserId)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    
    print("Saving data for player: " .. player.Name)
    
    local response = makeAPIRequest("POST", endpoint, data)
    
    if response and response.success then
        print("Successfully saved data for " .. player.Name)
        return true
    else
        warn("Failed to save data for " .. player.Name)
        return false
    end
end

-- Update specific player data fields
function PlayerDataManager:UpdatePlayerData(player, updates)
    local userId = tostring(player.UserId)
    
    if not self.LoadedPlayers[userId] then
        warn("Player data not loaded for " .. player.Name)
        return false
    end
    
    -- Update local data
    for key, value in pairs(updates) do
        self.LoadedPlayers[userId][key] = value
    end
    
    -- Save to API
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local response = makeAPIRequest("PUT", endpoint, updates)
    
    if response and response.success then
        print("Successfully updated data for " .. player.Name)
        return true
    else
        warn("Failed to update data for " .. player.Name)
        return false
    end
end

-- Get player data
function PlayerDataManager:GetPlayerData(player)
    local userId = tostring(player.UserId)
    return self.LoadedPlayers[userId]
end

-- Ban/Unban player
function PlayerDataManager:SetPlayerBanStatus(player, isBanned)
    local userId = tostring(player.UserId)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    
    local response = makeAPIRequest("PUT", endpoint, {IsBanned = isBanned})
    
    if response and response.success then
        print("Successfully " .. (isBanned and "banned" or "unbanned") .. " player: " .. player.Name)
        
        if isBanned then
            player:Kick("You have been banned from this game.")
        end
        
        return true
    else
        warn("Failed to " .. (isBanned and "ban" or "unban") .. " player: " .. player.Name)
        return false
    end
end

-- Clean up player data when they leave
function PlayerDataManager:CleanupPlayer(player)
    local userId = tostring(player.UserId)
    if self.LoadedPlayers[userId] then
        self.LoadedPlayers[userId] = nil
        print("Cleaned up data for " .. player.Name)
    end
end

-- Connect to player events
game["Players"].PlayerAdded:Connect(function(Player)
    -- Load player data when they join
    spawn(function()
        local playerData = PlayerDataManager:LoadPlayerData(Player)
        
        if playerData then
            -- You can fire events or set up player-specific data here
            print("Player " .. Player.Name .. " joined with level " .. playerData.level)
            
            -- Example: Set up leaderstats
            local leaderstats = Instance.new("Folder")
            leaderstats.Name = "leaderstats"
            leaderstats.Parent = Player
            
            local level = Instance.new("IntValue")
            level.Name = "Level"
            level.Value = playerData.level
            level.Parent = leaderstats
            
            local score = Instance.new("IntValue")
            score.Name = "Score"
            score.Value = playerData.score
            score.Parent = leaderstats
        end
    end)
end)

game["Players"].PlayerRemoving:Connect(function(Player)
    -- Save and cleanup when player leaves
    spawn(function()
        local playerData = PlayerDataManager:GetPlayerData(Player)
        if playerData then
            -- Update data with current values before saving
            local leaderstats = Player:FindFirstChild("leaderstats")
            if leaderstats then
                local level = leaderstats:FindFirstChild("Level")
                local score = leaderstats:FindFirstChild("Score")
                
                if level then playerData.level = level.Value end
                if score then playerData.score = score.Value end
                
                -- Save final data
                PlayerDataManager:SavePlayerData(Player, playerData)
            end
        end
        
        PlayerDataManager:CleanupPlayer(Player)
    end)
end)

-- Export the manager for use in other scripts
return PlayerDataManager
