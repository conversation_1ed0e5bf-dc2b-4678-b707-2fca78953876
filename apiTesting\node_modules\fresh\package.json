{"name": "fresh", "description": "HTTP response freshness testing", "version": "2.0.0", "author": "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://jongleberry.com)"], "license": "MIT", "keywords": ["fresh", "http", "conditional", "cache"], "repository": "jshttp/fresh", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "8.12.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.0.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.0", "nyc": "15.1.0"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}