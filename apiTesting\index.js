const express = require("express");
const app = express();
const PORT = 8080;

// Middleware to parse JSON requests
app.use(express.json());

// In-memory data storage (replace with database in production)
// Structure: gameData[gameId][userId] = saveData
let gameData = {};
// Global ban tracking: bannedUsers[userId] = true/false
let bannedUsers = {};

// Helper function to check if user is banned
function isUserBanned(userId) {
    return bannedUsers[userId] === true;
}

// Helper function to propagate ban across all games
function propagateBan(userId, isBanned) {
    bannedUsers[userId] = isBanned;

    // Update IsBanned field in all games for this user
    for (const gameId in gameData) {
        if (gameData[gameId][userId]) {
            gameData[gameId][userId].IsBanned = isBanned;
            gameData[gameId][userId].updatedAt = new Date().toISOString();
        }
    }
}

// GET /api/games/:gameId/users/:userId - Get user's save data for specific game
app.get('/api/games/:gameId/users/:userId', (req, res) => {
    const { gameId, userId } = req.params;

    if (!gameData[gameId] || !gameData[gameId][userId]) {
        return res.status(404).json({
            success: false,
            message: 'Save data not found for this user in this game'
        });
    }

    res.json({
        success: true,
        data: gameData[gameId][userId]
    });
});

// GET /api/games/:gameId/users - Get all users' save data for specific game
app.get('/api/games/:gameId/users', (req, res) => {
    const { gameId } = req.params;

    if (!gameData[gameId]) {
        return res.json({
            success: true,
            data: {},
            count: 0
        });
    }

    res.json({
        success: true,
        data: gameData[gameId],
        count: Object.keys(gameData[gameId]).length
    });
});

// GET /api/users/:userId - Get user's save data across all games
app.get('/api/users/:userId', (req, res) => {
    const { userId } = req.params;
    const userGames = {};

    for (const gameId in gameData) {
        if (gameData[gameId][userId]) {
            userGames[gameId] = gameData[gameId][userId];
        }
    }

    res.json({
        success: true,
        data: userGames,
        count: Object.keys(userGames).length,
        isBanned: isUserBanned(userId)
    });
});

// POST /api/games/:gameId/users/:userId - Create/Update user's save data for specific game
app.post('/api/games/:gameId/users/:userId', (req, res) => {
    const { gameId, userId } = req.params;
    const saveData = req.body;

    // Check if user is globally banned
    if (isUserBanned(userId)) {
        return res.status(403).json({
            success: false,
            message: 'User is banned and cannot save data'
        });
    }

    // Initialize game data structure if it doesn't exist
    if (!gameData[gameId]) {
        gameData[gameId] = {};
    }

    // Check if this is a ban update
    if (saveData.hasOwnProperty('IsBanned') && saveData.IsBanned === true) {
        // Propagate ban across all games
        propagateBan(userId, true);

        return res.json({
            success: true,
            message: 'User banned across all games',
            data: {
                userId,
                gameId,
                IsBanned: true,
                bannedAt: new Date().toISOString()
            }
        });
    }

    // Create or update save data
    const currentTime = new Date().toISOString();
    const isNewSave = !gameData[gameId][userId];

    gameData[gameId][userId] = {
        ...saveData,
        userId,
        gameId,
        IsBanned: isUserBanned(userId), // Always reflect current ban status
        createdAt: isNewSave ? currentTime : (gameData[gameId][userId]?.createdAt || currentTime),
        updatedAt: currentTime
    };

    res.status(isNewSave ? 201 : 200).json({
        success: true,
        message: isNewSave ? 'Save data created successfully' : 'Save data updated successfully',
        data: gameData[gameId][userId]
    });
});

// PUT /api/games/:gameId/users/:userId - Update user's save data for specific game
app.put('/api/games/:gameId/users/:userId', (req, res) => {
    const { gameId, userId } = req.params;
    const updateData = req.body;

    // Check if save data exists
    if (!gameData[gameId] || !gameData[gameId][userId]) {
        return res.status(404).json({
            success: false,
            message: 'Save data not found for this user in this game'
        });
    }

    // Check if user is globally banned (unless this is an unban operation)
    if (isUserBanned(userId) && !(updateData.hasOwnProperty('IsBanned') && updateData.IsBanned === false)) {
        return res.status(403).json({
            success: false,
            message: 'User is banned and cannot update save data'
        });
    }

    // Check if this is a ban/unban update
    if (updateData.hasOwnProperty('IsBanned')) {
        propagateBan(userId, updateData.IsBanned);

        return res.json({
            success: true,
            message: updateData.IsBanned ? 'User banned across all games' : 'User unbanned across all games',
            data: {
                userId,
                gameId,
                IsBanned: updateData.IsBanned,
                updatedAt: new Date().toISOString()
            }
        });
    }

    // Update save data (preserving ban status)
    gameData[gameId][userId] = {
        ...gameData[gameId][userId],
        ...updateData,
        IsBanned: isUserBanned(userId), // Always preserve current ban status
        updatedAt: new Date().toISOString()
    };

    res.json({
        success: true,
        message: 'Save data updated successfully',
        data: gameData[gameId][userId]
    });
});

// DELETE /api/games/:gameId/users/:userId - Delete user's save data for specific game
app.delete('/api/games/:gameId/users/:userId', (req, res) => {
    const { gameId, userId } = req.params;

    if (!gameData[gameId] || !gameData[gameId][userId]) {
        return res.status(404).json({
            success: false,
            message: 'Save data not found for this user in this game'
        });
    }

    const deletedData = gameData[gameId][userId];
    delete gameData[gameId][userId];

    // Clean up empty game data
    if (Object.keys(gameData[gameId]).length === 0) {
        delete gameData[gameId];
    }

    res.json({
        success: true,
        message: 'Save data deleted successfully',
        data: deletedData
    });
});

// DELETE /api/users/:userId - Delete user's save data across all games
app.delete('/api/users/:userId', (req, res) => {
    const { userId } = req.params;
    let deletedCount = 0;
    const deletedData = {};

    // Remove user data from all games
    for (const gameId in gameData) {
        if (gameData[gameId][userId]) {
            deletedData[gameId] = gameData[gameId][userId];
            delete gameData[gameId][userId];
            deletedCount++;

            // Clean up empty game data
            if (Object.keys(gameData[gameId]).length === 0) {
                delete gameData[gameId];
            }
        }
    }

    // Remove from banned users list
    delete bannedUsers[userId];

    if (deletedCount === 0) {
        return res.status(404).json({
            success: false,
            message: 'No save data found for this user'
        });
    }

    res.json({
        success: true,
        message: `User data deleted from ${deletedCount} games`,
        data: deletedData,
        deletedCount
    });
});

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Game Save Data API is running!',
        description: 'API for managing user save data across multiple games with global ban system',
        endpoints: {
            'GET /api/games/:gameId/users/:userId': 'Get user save data for specific game',
            'GET /api/games/:gameId/users': 'Get all users save data for specific game',
            'GET /api/users/:userId': 'Get user save data across all games',
            'POST /api/games/:gameId/users/:userId': 'Create/Update user save data for specific game',
            'PUT /api/games/:gameId/users/:userId': 'Update user save data for specific game',
            'DELETE /api/games/:gameId/users/:userId': 'Delete user save data for specific game',
            'DELETE /api/users/:userId': 'Delete user save data across all games'
        },
        banSystem: {
            description: 'Global ban system - banning a user in one game bans them in all games',
            banField: 'IsBanned (boolean)',
            banBehavior: 'Setting IsBanned=true propagates across all games for that user'
        }
    });
});

app.listen(
    PORT,
    () => console.log(`API server is running on http://localhost:${PORT}`)
)