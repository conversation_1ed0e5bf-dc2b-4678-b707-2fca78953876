const express = require("express");
const app = express();
const PORT = 8080;

// Middleware to parse JSON requests
app.use(express.json());

// In-memory data storage (replace with database in production)
let data = [];
let nextId = 1;

// GET /api/data - Retrieve all data
app.get('/api/data', (req, res) => {
    res.json({
        success: true,
        data: data,
        count: data.length
    });
});

// GET /api/data/:id - Retrieve specific item by ID
app.get('/api/data/:id', (req, res) => {
    const id = parseInt(req.params.id);
    const item = data.find(item => item.id === id);

    if (!item) {
        return res.status(404).json({
            success: false,
            message: 'Item not found'
        });
    }

    res.json({
        success: true,
        data: item
    });
});

// POST /api/data - Create new data
app.post('/api/data', (req, res) => {
    const { name, description, value } = req.body;

    // Basic validation
    if (!name) {
        return res.status(400).json({
            success: false,
            message: 'Name is required'
        });
    }

    const newItem = {
        id: nextId++,
        name,
        description: description || '',
        value: value || null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    data.push(newItem);

    res.status(201).json({
        success: true,
        message: 'Item created successfully',
        data: newItem
    });
});

// PUT /api/data/:id - Update existing data
app.put('/api/data/:id', (req, res) => {
    const id = parseInt(req.params.id);
    const itemIndex = data.findIndex(item => item.id === id);

    if (itemIndex === -1) {
        return res.status(404).json({
            success: false,
            message: 'Item not found'
        });
    }

    const { name, description, value } = req.body;

    // Update the item
    data[itemIndex] = {
        ...data[itemIndex],
        name: name || data[itemIndex].name,
        description: description !== undefined ? description : data[itemIndex].description,
        value: value !== undefined ? value : data[itemIndex].value,
        updatedAt: new Date().toISOString()
    };

    res.json({
        success: true,
        message: 'Item updated successfully',
        data: data[itemIndex]
    });
});

// DELETE /api/data/:id - Delete data
app.delete('/api/data/:id', (req, res) => {
    const id = parseInt(req.params.id);
    const itemIndex = data.findIndex(item => item.id === id);

    if (itemIndex === -1) {
        return res.status(404).json({
            success: false,
            message: 'Item not found'
        });
    }

    const deletedItem = data.splice(itemIndex, 1)[0];

    res.json({
        success: true,
        message: 'Item deleted successfully',
        data: deletedItem
    });
});

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'API is running!',
        endpoints: {
            'GET /api/data': 'Get all items',
            'GET /api/data/:id': 'Get item by ID',
            'POST /api/data': 'Create new item',
            'PUT /api/data/:id': 'Update item by ID',
            'DELETE /api/data/:id': 'Delete item by ID'
        }
    });
});

app.listen(
    PORT,
    () => console.log(`API server is running on http://localhost:${PORT}`)
)